import {
  ExchangeService,
  ExchangeVersion,
  WebCredentials,
  Uri,
  WellKnownFolderName,
  ItemView,
  ItemId,
  EmailMessage,
  MimeContent,
  PropertySet,
  BasePropertySet,
  EmailMessageSchema,
  FileAttachment,
  ItemAttachment,
  EmailAddressCollection
} from 'ews-javascript-api'
import { retry } from '../decorators/retry'

export interface EmailContent {
  id: string
  subject: string
  sender: string
  recipients: string[]
  ccRecipients: string[]
  body: string
  htmlBody: string
  receivedTime: Date
  attachments: EmailAttachment[]
  metadata: {
    messageId: string
    importance: string
    hasAttachments: boolean
  }
}

export interface EmailAttachment {
  name: string
  contentType: string
  size: number
  content: Buffer
  isInline: boolean
}
 

export class EWSMailService {
  private service: ExchangeService | null = null
  private isConnected = false
  
  /**
   * 连接到EWS服务（移除测试连接，延迟到实际调用时）
   */
  async connect(domainAccount: string, password: string): Promise<boolean> {
    this.service = new ExchangeService(ExchangeVersion.Exchange2016)
    this.service.Credentials = new WebCredentials(domainAccount, password)
    this.service.Url = new Uri(process.env.EWS_URL || '');
    
    // 不再测试连接，延迟到实际调用时
    this.isConnected = true
    return true
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.service = null
    this.isConnected = false
  }

  @retry({maxRetries : 10})
  async sendMail(eml:Buffer){  
    if(!this.service) return;

    // 创建 EmailMessage 并设置 MimeContent
    const message = new EmailMessage(this.service);
    message.MimeContent = new MimeContent("UTF-8", eml.toString("base64")); 
    await message.SendAndSaveCopy();
  }

  
  @retry({maxRetries : 10})
  async saveToDraft(eml:Buffer){  
    if(!this.service) return;

    // 创建 EmailMessage 并设置 MimeContent
    const message = new EmailMessage(this.service);
    message.MimeContent = new MimeContent("UTF-8", eml.toString("base64")); 
    await message.Save(WellKnownFolderName.Drafts);
  }

  /**
   * 获取连接状态
   */
  isServiceConnected(): boolean {
    return this.isConnected && this.service !== null
  }
 
}
 
 