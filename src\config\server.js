/* eslint-disable @typescript-eslint/no-require-imports */
const path = require('path');
const os = require('os');

/**
 * 服务器配置
 */
const serverConfig = {
  // 基本配置
  port: process.env.PORT || 3100,
  host: process.env.HOST || 'localhost',
  
  // 环境配置
  isDevelopment: process.env.NODE_ENV !== 'production',
  isProduction: process.env.NODE_ENV === 'production',
  
  // HTTPS 配置
  https: {
    enabled: process.env.HTTPS_ENABLED === 'true' || process.env.NODE_ENV !== 'production',
    certDir: process.env.CERT_DIR || path.join(os.homedir(), '.office-addin-dev-certs'),
    keyFile: process.env.HTTPS_KEY_FILE || 'localhost.key',
    certFile: process.env.HTTPS_CERT_FILE || 'localhost.crt',
  },
  
  // 定时任务配置
  cron: {
    enabled: process.env.TASK_SCHEDULER_ENABLED === 'true',
    timezone: process.env.TASK_SCHEDULER_TIMEZONE || 'Asia/Shanghai',
    maxConcurrent: parseInt(process.env.TASK_SCHEDULER_MAX_CONCURRENT || '1'),
    defaultSchedule: process.env.TASK_SCHEDULER_CRON || '*/5 * * * *',
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: process.env.LOG_CONSOLE !== 'false',
    enableFile: process.env.LOG_FILE === 'true',
    logDir: process.env.LOG_DIR || path.join(process.cwd(), 'logs'),
  },
  
  // 安全配置
  security: {
    trustProxy: process.env.TRUST_PROXY === 'true',
    corsOrigin: process.env.CORS_ORIGIN || '*',
    rateLimitEnabled: process.env.RATE_LIMIT_ENABLED === 'true',
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'),
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15分钟
  },
  
  // 性能配置
  performance: {
    compression: process.env.COMPRESSION !== 'false',
    keepAliveTimeout: parseInt(process.env.KEEP_ALIVE_TIMEOUT || '5000'),
    headersTimeout: parseInt(process.env.HEADERS_TIMEOUT || '60000'),
  },
  
  // 关闭配置
  shutdown: {
    gracefulTimeout: parseInt(process.env.GRACEFUL_TIMEOUT || '10000'),
    forceExitTimeout: parseInt(process.env.FORCE_EXIT_TIMEOUT || '15000'),
  }
};

/**
 * 获取 HTTPS 选项
 */
const getHttpsOptions = () => {
  if (!serverConfig.https.enabled) {
    return null;
  }
  
  try {
    const fs = require('fs');
    const keyPath = path.join(serverConfig.https.certDir, serverConfig.https.keyFile);
    const certPath = path.join(serverConfig.https.certDir, serverConfig.https.certFile);
    
    return {
      key: fs.readFileSync(keyPath),
      cert: fs.readFileSync(certPath),
    };
  } catch (error) {
    console.warn('HTTPS 证书加载失败，将使用 HTTP:', error.message);
    return null;
  }
};

/**
 * 验证配置
 */
const validateConfig = () => {
  const errors = [];
  
  // 验证端口
  if (isNaN(serverConfig.port) || serverConfig.port < 1 || serverConfig.port > 65535) {
    errors.push('端口号必须是 1-65535 之间的数字');
  }
  
  // 验证定时任务配置
  if (serverConfig.cron.enabled) {
    if (isNaN(serverConfig.cron.maxConcurrent) || serverConfig.cron.maxConcurrent < 1) {
      errors.push('最大并发任务数必须是大于 0 的数字');
    }
  }
  
  // 验证超时配置
  if (isNaN(serverConfig.shutdown.gracefulTimeout) || serverConfig.shutdown.gracefulTimeout < 0) {
    errors.push('优雅关闭超时时间必须是非负数');
  }
  
  if (errors.length > 0) {
    throw new Error('配置验证失败:\n' + errors.join('\n'));
  }
  
  return true;
};

/**
 * 打印配置信息
 */
const printConfig = () => {
  console.log('=== 服务器配置 ===');
  console.log(`环境: ${serverConfig.isDevelopment ? '开发' : '生产'}`);
  console.log(`端口: ${serverConfig.port}`);
  console.log(`HTTPS: ${serverConfig.https.enabled ? '启用' : '禁用'}`);
  console.log(`定时任务: ${serverConfig.cron.enabled ? '启用' : '禁用'}`);
  if (serverConfig.cron.enabled) {
    console.log(`定时任务时区: ${serverConfig.cron.timezone}`);
    console.log(`最大并发任务: ${serverConfig.cron.maxConcurrent}`);
  }
  console.log('==================');
};

module.exports = {
  serverConfig,
  getHttpsOptions,
  validateConfig,
  printConfig
};
