/* eslint-disable @typescript-eslint/no-require-imports */
const { createServer: createHttpsServer } = require('https');
const { createServer: createHttpServer } = require('http');
const { parse } = require('url');
const next = require('next');
const { initializeCronJobs } = require('./src/lib/cronJobs');
const { serverConfig, getHttpsOptions, validateConfig, printConfig } = require('./src/config/server');

// 验证配置
try {
  validateConfig();
} catch (error) {
  console.error('配置错误:', error.message);
  process.exit(1);
}

// 打印配置信息
printConfig();

// Next.js 应用配置
const app = next({ dev: serverConfig.isDevelopment });
const handle = app.getRequestHandler();

// 获取 HTTPS 配置
const httpsOptions = getHttpsOptions();

// 定时任务配置
let cronManager = null;
const setupCronJobs = () => {
  try {
    cronManager = initializeCronJobs();
    console.log('定时任务管理器已启动');
  } catch (error) {
    console.error('定时任务启动失败:', error);
  }
};

// 启动服务器
app.prepare().then(() => {
  const requestHandler = (req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  };

  let server;
  let protocol;

  if (serverConfig.isDevelopment && httpsOptions) {
    // 开发环境使用 HTTPS
    server = createHttpsServer(httpsOptions, requestHandler);
    protocol = 'https';
  } else {
    // 生产环境或开发环境无证书时使用 HTTP
    server = createHttpServer(requestHandler);
    protocol = 'http';
  }

  // 配置服务器选项
  if (serverConfig.performance.keepAliveTimeout) {
    server.keepAliveTimeout = serverConfig.performance.keepAliveTimeout;
  }
  if (serverConfig.performance.headersTimeout) {
    server.headersTimeout = serverConfig.performance.headersTimeout;
  }

  // 启动服务器
  server.listen(serverConfig.port, serverConfig.host, (err) => {
    if (err) throw err;
    console.log(`> ${serverConfig.isDevelopment ? '开发' : '生产'}服务器已启动: ${protocol}://${serverConfig.host}:${serverConfig.port}`);
    setupCronJobs();
  });

  // 优雅关闭
  const gracefulShutdown = (signal) => {
    console.log(`收到 ${signal} 信号，正在关闭服务器...`);

    // 停止定时任务
    if (cronManager) {
      console.log('正在停止定时任务...');
      cronManager.stopAllJobs();
    }

    // 关闭服务器
    server.close(() => {
      console.log('服务器已关闭');
      process.exit(0);
    });

    // 优雅关闭超时
    setTimeout(() => {
      console.warn('优雅关闭超时，强制退出服务器');
      process.exit(1);
    }, serverConfig.shutdown.gracefulTimeout);

    // 强制退出超时
    setTimeout(() => {
      console.error('强制退出超时，立即终止进程');
      process.exit(1);
    }, serverConfig.shutdown.forceExitTimeout);
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // 处理未捕获的异常
  process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    gracefulShutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason, 'at:', promise);
    gracefulShutdown('unhandledRejection');
  });
});