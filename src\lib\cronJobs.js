/* eslint-disable @typescript-eslint/no-require-imports */
const cron = require('node-cron');

/**
 * 定时任务管理器
 */
class CronJobManager {
  constructor() {
    this.jobs = new Map();
    this.isEnabled = process.env.TASK_SCHEDULER_ENABLED === 'true';
    this.timezone = process.env.TASK_SCHEDULER_TIMEZONE || 'Asia/Shanghai';
  }

  /**
   * 添加定时任务
   * @param {string} name - 任务名称
   * @param {string} schedule - cron 表达式
   * @param {Function} task - 任务函数
   * @param {Object} options - 选项
   */
  addJob(name, schedule, task, options = {}) {
    if (!this.isEnabled) {
      console.log(`定时任务已禁用，跳过任务: ${name}`);
      return;
    }

    const jobOptions = {
      scheduled: false,
      timezone: this.timezone,
      ...options
    };

    try {
      const job = cron.schedule(schedule, async () => {
        console.log(`[${new Date().toISOString()}] 开始执行定时任务: ${name}`);
        try {
          await task();
          console.log(`[${new Date().toISOString()}] 定时任务执行成功: ${name}`);
        } catch (error) {
          console.error(`[${new Date().toISOString()}] 定时任务执行失败: ${name}`, error);
        }
      }, jobOptions);

      this.jobs.set(name, job);
      console.log(`定时任务已添加: ${name} (${schedule})`);
    } catch (error) {
      console.error(`添加定时任务失败: ${name}`, error);
    }
  }

  /**
   * 启动指定任务
   * @param {string} name - 任务名称
   */
  startJob(name) {
    const job = this.jobs.get(name);
    if (job) {
      job.start();
      console.log(`定时任务已启动: ${name}`);
    } else {
      console.warn(`定时任务不存在: ${name}`);
    }
  }

  /**
   * 停止指定任务
   * @param {string} name - 任务名称
   */
  stopJob(name) {
    const job = this.jobs.get(name);
    if (job) {
      job.stop();
      console.log(`定时任务已停止: ${name}`);
    } else {
      console.warn(`定时任务不存在: ${name}`);
    }
  }

  /**
   * 启动所有任务
   */
  startAllJobs() {
    if (!this.isEnabled) {
      console.log('定时任务已禁用');
      return;
    }

    this.jobs.forEach((job, name) => {
      job.start();
      console.log(`定时任务已启动: ${name}`);
    });
    console.log(`所有定时任务已启动，共 ${this.jobs.size} 个任务`);
  }

  /**
   * 停止所有任务
   */
  stopAllJobs() {
    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(`定时任务已停止: ${name}`);
    });
    console.log('所有定时任务已停止');
  }

  /**
   * 删除指定任务
   * @param {string} name - 任务名称
   */
  removeJob(name) {
    const job = this.jobs.get(name);
    if (job) {
      job.destroy();
      this.jobs.delete(name);
      console.log(`定时任务已删除: ${name}`);
    } else {
      console.warn(`定时任务不存在: ${name}`);
    }
  }

  /**
   * 获取任务状态
   * @param {string} name - 任务名称
   */
  getJobStatus(name) {
    const job = this.jobs.get(name);
    if (job) {
      return {
        name,
        running: job.running,
        scheduled: job.scheduled
      };
    }
    return null;
  }

  /**
   * 获取所有任务状态
   */
  getAllJobsStatus() {
    const status = [];
    this.jobs.forEach((job, name) => {
      status.push({
        name,
        running: job.running,
        scheduled: job.scheduled
      });
    });
    return status;
  }
}

// 示例任务函数
const sampleTasks = {
  /**
   * 示例任务：系统健康检查
   */
  healthCheck: async () => {
    console.log('执行系统健康检查...');
    // 在这里添加健康检查逻辑
    // 例如：检查数据库连接、检查外部服务状态等
  },

  /**
   * 示例任务：清理临时文件
   */
  cleanupTempFiles: async () => {
    console.log('清理临时文件...');
    // 在这里添加清理逻辑
    // 例如：删除过期的临时文件、清理缓存等
  },

  /**
   * 示例任务：数据备份
   */
  dataBackup: async () => {
    console.log('执行数据备份...');
    // 在这里添加备份逻辑
    // 例如：备份数据库、备份重要文件等
  },

  /**
   * 示例任务：发送报告
   */
  sendReport: async () => {
    console.log('发送定期报告...');
    // 在这里添加报告发送逻辑
    // 例如：生成报告、发送邮件等
  }
};

/**
 * 初始化定时任务
 */
const initializeCronJobs = () => {
  const cronManager = new CronJobManager();

  // 添加示例任务
  cronManager.addJob('healthCheck', '*/5 * * * *', sampleTasks.healthCheck); // 每5分钟
  cronManager.addJob('cleanupTempFiles', '0 2 * * *', sampleTasks.cleanupTempFiles); // 每天凌晨2点
  cronManager.addJob('dataBackup', '0 3 * * 0', sampleTasks.dataBackup); // 每周日凌晨3点
  cronManager.addJob('sendReport', '0 9 * * 1', sampleTasks.sendReport); // 每周一上午9点

  // 从环境变量读取自定义任务配置
  const customCron = process.env.TASK_SCHEDULER_CRON;
  if (customCron) {
    cronManager.addJob('customTask', customCron, async () => {
      console.log('执行自定义定时任务...');
      // 在这里添加自定义任务逻辑
    });
  }

  // 启动所有任务
  cronManager.startAllJobs();

  return cronManager;
};

module.exports = {
  CronJobManager,
  sampleTasks,
  initializeCronJobs
};
